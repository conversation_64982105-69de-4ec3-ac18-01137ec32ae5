import { useState, useEffect, useCallback, useRef } from 'react';

import { FirebaseService } from '../services/FirebaseService';
import { UserService } from '../services/UserService';
import { ChatService } from '../services/ChatService';
import { UserStore } from '../store/store';
import { GroupChatItem, GroupMessage, GroupMember } from '../types/chat_type';

interface UseFirebaseGroupChatReturn {
  eventChats: GroupChatItem[];
  communityChats: GroupChatItem[];
  loading: boolean;
  error: string | null;
  loadGroupChats: () => Promise<void>;
  sendGroupMessage: (groupId: string, messageText: string, imageFiles?: File) => Promise<boolean>;
  listenToGroupMessages: (
    groupId: string,
    callback: (messages: GroupMessage[]) => void
  ) => () => void;
  joinGroupChat: (groupId: string) => Promise<boolean>;
  leaveGroupChat: (groupId: string) => Promise<boolean>;
  getGroupMembers: (groupId: string) => Promise<GroupMember[]>;
}

// Helper function to convert Firebase group message to GiftedChat format
const convertFirebaseGroupMessageToGiftedChat = (
  msg: any,
  users: { [userId: string]: any }
): GroupMessage => {
  const user = users[msg.senderId] || {
    id: msg.senderId,
    name: 'Unknown User',
    avatar:
      'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
  };

  return {
    _id: msg.id,
    text: msg.messageText || '',
    createdAt: new Date(msg.messageTime),
    user: {
      _id: user.id,
      name: user.name,
      avatar: user.avatar,
    },
    ...(msg.uploadUrl && { image: msg.uploadUrl }),
    groupId: msg.groupId || '',
  };
};

export const useFirebaseGroupChat = (): UseFirebaseGroupChatReturn => {
  const [eventChats, setEventChats] = useState<GroupChatItem[]>([]);
  const [communityChats, setCommunityChats] = useState<GroupChatItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const groupListeners = useRef<{ [groupId: string]: () => void }>({});

  const userData = UserStore((state: any) => state.user);

  // Load user's group chats
  const loadGroupChats = useCallback(async () => {
    if (!userData?.id) return;

    try {
      setLoading(true);
      setError(null);

      // Get user's group chat IDs from Firebase
      const userGroupChats = await FirebaseService.getUserGroupChats(userData.id);
      const eventChatItems: GroupChatItem[] = [];
      const communityChatItems: GroupChatItem[] = [];

      // For each group chat, get the group details and last message
      for (const [groupId, groupType] of Object.entries(userGroupChats)) {
        try {
          // Get group chat details
          const groupChat = await FirebaseService.getGroupChat(groupId);
          if (!groupChat) continue;

          // Get last message - handle both object and string formats
          let lastMessageText = 'No messages yet';
          let lastMessageTime = Date.now();

          if (groupChat.messages) {
            if (typeof groupChat.messages === 'string') {
              // Handle string format like "shall we play a game..."
              lastMessageText = groupChat.messages;
            } else if (typeof groupChat.messages === 'object') {
              // Handle object format with message IDs
              const messageKeys = Object.keys(groupChat.messages);
              if (messageKeys.length > 0) {
                // Get the most recent message
                const messages = Object.values(groupChat.messages);
                const sortedMessages = messages.sort(
                  (a: any, b: any) => b.messageTime - a.messageTime
                );
                if (sortedMessages.length > 0) {
                  lastMessageText = sortedMessages[0].messageText || 'No messages yet';
                  lastMessageTime = sortedMessages[0].messageTime || Date.now();
                }
              }
            }
          }

          // Create group chat item
          const groupChatItem: GroupChatItem = {
            id: groupId,
            name: groupChat.groupName,
            avatar: groupChat.groupIcon,
            lastMessage: lastMessageText,
            timestamp: new Date(lastMessageTime).toLocaleTimeString([], {
              hour: '2-digit',
              minute: '2-digit',
            }),
            chatId: groupId,
            groupType: groupChat.groupType,
            eventId: groupChat.eventId,
            communityId: groupChat.communityId,
            groupCreatedBy: groupChat.groupCreatedBy,
            memberCount: Object.keys(groupChat.members || {}).length,
            members: groupChat.members || {},
          };

          // Add to appropriate array based on type
          if (groupType === 'EVENT') {
            eventChatItems.push(groupChatItem);
          } else if (groupType === 'COMMUNITY') {
            communityChatItems.push(groupChatItem);
          }
        } catch (error) {
          console.error(`Error loading group chat ${groupId}:`, error);
        }
      }

      // Sort by last message time (most recent first)
      const sortByTimestamp = (a: GroupChatItem, b: GroupChatItem) => {
        if (!a.timestamp && !b.timestamp) return 0;
        if (!a.timestamp) return 1;
        if (!b.timestamp) return -1;
        return b.timestamp.localeCompare(a.timestamp);
      };

      eventChatItems.sort(sortByTimestamp);
      communityChatItems.sort(sortByTimestamp);

      setEventChats(eventChatItems);
      setCommunityChats(communityChatItems);
    } catch (error) {
      console.error('Error loading group chats:', error);
      setError('Failed to load group chats');
    } finally {
      setLoading(false);
    }
  }, [userData?.id]);

  // Send a group message
  const sendGroupMessage = useCallback(
    async (groupId: string, messageText: string, imageFiles?: File): Promise<boolean> => {
      if (!userData?.id) return false;
      console.log(groupId, userData.id, messageText, imageFiles);
      try {
        // Use ChatService to send group message to backend
        await ChatService.sendGroupMessage(imageFiles as File, groupId, userData.id, messageText);
        return true;
      } catch (error) {
        console.error('Error sending group message:', error);
        setError('Failed to send message');
        return false;
      }
    },
    [userData?.id]
  );

  // Listen to messages in a specific group chat
  const listenToGroupMessages = useCallback(
    (groupId: string, callback: (messages: GroupMessage[]) => void) => {
      if (!groupId) return () => {};

      return FirebaseService.listenToGroupMessages(groupId, async (firebaseMessages) => {
        try {
          // Get user info for all participants
          const userIds = [...new Set(firebaseMessages.map((msg) => msg.senderId))];
          const users: { [userId: string]: any } = {};

          // Load user data for message senders
          for (const userId of userIds) {
            try {
              if (userId === userData?.id) {
                // Current user
                users[userId] = {
                  id: userData.id,
                  name: userData.fullName || userData.username || 'You',
                  avatar:
                    userData.profilePicture ||
                    'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
                };
              } else {
                // Other users - get from Firebase first, then from backend
                const firebaseUser = await FirebaseService.getFirebaseUser(userId);
                if (firebaseUser) {
                  try {
                    const otherUserResponse = await UserService.getOtherUser({
                      email: firebaseUser.email,
                    });
                    if (otherUserResponse.success && otherUserResponse.user) {
                      users[userId] = {
                        id: userId,
                        name: otherUserResponse.user.fullName || otherUserResponse.user.username,
                        avatar:
                          otherUserResponse.user.profilePicture ||
                          'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
                      };
                    }
                  } catch (backendError) {
                    console.error('Error fetching user from backend:', backendError);
                    // Fallback to Firebase data
                    users[userId] = {
                      id: userId,
                      name: firebaseUser.fullName || 'Unknown User',
                      avatar:
                        'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
                    };
                  }
                } else {
                  // Fallback for unknown users
                  users[userId] = {
                    id: userId,
                    name: 'Unknown User',
                    avatar:
                      'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
                  };
                }
              }
            } catch (error) {
              console.error(`Error loading user ${userId}:`, error);
              users[userId] = {
                id: userId,
                name: 'Unknown User',
                avatar:
                  'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
              };
            }
          }

          // Convert Firebase messages to GiftedChat format
          const giftedChatMessages = firebaseMessages.map((msg: any) =>
            convertFirebaseGroupMessageToGiftedChat(msg, users)
          );

          // Reverse for GiftedChat (newest first)
          callback(giftedChatMessages.reverse());
        } catch (error) {
          console.error('Error processing group messages:', error);
          callback([]);
        }
      });
    },
    [userData]
  );

  // Join a group chat
  const joinGroupChat = useCallback(
    async (groupId: string): Promise<boolean> => {
      if (!userData?.id) return false;

      try {
        await FirebaseService.joinGroupChat(groupId, userData.id);
        // Reload group chats to reflect the change
        await loadGroupChats();
        return true;
      } catch (error) {
        console.error('Error joining group chat:', error);
        setError('Failed to join group chat');
        return false;
      }
    },
    [userData?.id, loadGroupChats]
  );

  // Leave a group chat
  const leaveGroupChat = useCallback(
    async (groupId: string): Promise<boolean> => {
      if (!userData?.id) return false;

      try {
        await FirebaseService.leaveGroupChat(groupId, userData.id);
        // Reload group chats to reflect the change
        await loadGroupChats();
        return true;
      } catch (error) {
        console.error('Error leaving group chat:', error);
        setError('Failed to leave group chat');
        return false;
      }
    },
    [userData?.id, loadGroupChats]
  );

  // Get group members
  const getGroupMembers = useCallback(
    async (groupId: string): Promise<GroupMember[]> => {
      try {
        const groupChat = await FirebaseService.getGroupChat(groupId);
        if (!groupChat || !groupChat.members) return [];

        const members: GroupMember[] = [];
        const memberIds = Object.keys(groupChat.members);

        for (const memberId of memberIds) {
          try {
            if (memberId === userData?.id) {
              // Current user
              members.push({
                id: userData.id,
                name: userData.fullName || userData.username || 'You',
                avatar:
                  userData.profilePicture ||
                  'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
                email: userData.email,
                fullName: userData.fullName || userData.username || '',
                isAdmin: groupChat.groupCreatedBy === userData.id,
              });
            } else {
              // Other members
              const firebaseUser = await FirebaseService.getFirebaseUser(memberId);
              if (firebaseUser) {
                try {
                  const otherUserResponse = await UserService.getOtherUser({
                    email: firebaseUser.email,
                  });
                  if (otherUserResponse.success && otherUserResponse.user) {
                    members.push({
                      id: memberId,
                      name: otherUserResponse.user.fullName || otherUserResponse.user.username,
                      avatar:
                        otherUserResponse.user.profilePicture ||
                        'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
                      email: firebaseUser.email,
                      fullName: firebaseUser.fullName,
                      isAdmin: groupChat.groupCreatedBy === memberId,
                    });
                  }
                } catch (backendError) {
                  console.error('Error fetching member from backend:', backendError);
                  // Fallback to Firebase data
                  members.push({
                    id: memberId,
                    name: firebaseUser.fullName || 'Unknown User',
                    avatar:
                      'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
                    email: firebaseUser.email,
                    fullName: firebaseUser.fullName,
                    isAdmin: groupChat.groupCreatedBy === memberId,
                  });
                }
              }
            }
          } catch (error) {
            console.error(`Error loading member ${memberId}:`, error);
          }
        }

        return members;
      } catch (error) {
        console.error('Error getting group members:', error);
        return [];
      }
    },
    [userData]
  );

  // Load group chats on mount and when user changes
  useEffect(() => {
    if (userData?.id) {
      loadGroupChats();
    }

    // Cleanup listeners on unmount or user change
    return () => {
      Object.values(groupListeners.current).forEach((unsubscribe) => {
        unsubscribe();
      });
      groupListeners.current = {};
    };
  }, [userData?.id, loadGroupChats]);

  return {
    eventChats,
    communityChats,
    loading,
    error,
    loadGroupChats,
    sendGroupMessage,
    listenToGroupMessages,
    joinGroupChat,
    leaveGroupChat,
    getGroupMembers,
  };
};
