import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  TextInput,
  ActivityIndicator,
  BackHandler,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import { FriendService } from '~/services/FriendService';
import { UserStore } from '~/store/store';
import { Toast } from 'toastify-react-native';

interface Friend {
  id: string;
  name: string;
  avatar: string;
  status: string;
}

interface NewChatSheetProps {
  bottomSheetRef: React.RefObject<BottomSheet>;
  onSelectFriend: (friend: Friend) => void;
  colors: any;
  isDark: boolean;
  friendsWithoutChats?: Friend[]; // Add this prop
  isOpen?: boolean; // Add this prop to track if sheet is open
  onClose?: () => void; // Add this prop to close the sheet
}

const NewChatSheet = ({
  bottomSheetRef,
  onSelectFriend,
  colors,
  isDark,
  friendsWithoutChats = [],
  isOpen = false,
  onClose,
}: NewChatSheetProps) => {
  const user = UserStore((state: any) => state.user);
  const [searchQuery, setSearchQuery] = useState('');
  const [friends, setFriends] = useState<Friend[]>([]);
  const [filteredFriends, setFilteredFriends] = useState<Friend[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch friends on component mount
  useEffect(() => {
    fetchFriends();
  }, [user?.id]);

  // Handle back button to close the sheet when it's open
  useEffect(() => {
    const handleBackPress = () => {
      if (isOpen) {
        // Close the new chat sheet
        if (onClose) {
          onClose();
        } else {
          bottomSheetRef.current?.close();
        }
        return true; // Prevent default back behavior
      }
      return false; // Let default back behavior happen
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);

    return () => {
      backHandler.remove();
    };
  }, [isOpen, onClose, bottomSheetRef]);

  const fetchFriends = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const response = await FriendService.getFriends(user.id);
      if (response.success && response.body) {
        // Transform the response to match our Friend interface
        const friendsData = response.body.friends.map((friend: any) => ({
          id: friend.id,
          name: friend.fullName || friend.name,
          avatar:
            friend.profilePicture?.[0]?.secureUrl ||
            friend.profilePhoto ||
            'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
          status: friend.isOnline ? 'online' : 'offline',
        }));
        setFriends(friendsData);
        setFilteredFriends(friendsData);
      } else {
        setFriends([]);
        setFilteredFriends([]);
      }
    } catch (error) {
      console.error('Error fetching friends:', error);
      setFriends([]);
      setFilteredFriends([]);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load friends',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    if (text.trim() === '') {
      setFilteredFriends(friends);
    } else {
      const filtered = friends.filter((friend) =>
        friend.name.toLowerCase().includes(text.toLowerCase())
      );
      setFilteredFriends(filtered);
    }
  };

  const renderFriend = ({ item }: { item: Friend }) => (
    <TouchableOpacity
      className="flex-row items-center border-b px-4 py-3"
      style={{ borderBottomColor: colors.grey5 }}
      onPress={() => onSelectFriend(item)}>
      <View className="relative">
        <Image source={{ uri: item.avatar }} className="h-12 w-12 rounded-full" />
        <View
          className="absolute bottom-0 right-0 h-3 w-3 rounded-full border-2"
          style={{
            borderColor: isDark ? colors.background : colors.root,
          }}
        />
      </View>
      <View className="ml-3">
        <Text className="font-medium text-base" style={{ color: colors.foreground }}>
          {item.name}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <BottomSheetView style={{ backgroundColor: colors.background, flex: 1 }}>
      <View className="px-4 pb-6 pt-2">
        <Text className="mb-4 text-center font-bold text-xl" style={{ color: colors.foreground }}>
          New Chat
        </Text>

        <View className="mb-4">
          <View
            className="flex-row items-center rounded-xl px-3 py-2.5"
            style={{ backgroundColor: colors.grey5 }}>
            <Ionicons name="search" size={20} color={colors.grey} />
            <TextInput
              className="ml-2 flex-1 text-base"
              placeholder="Search friends..."
              placeholderTextColor={colors.grey}
              value={searchQuery}
              onChangeText={handleSearch}
              style={{ color: colors.foreground }}
            />
            {searchQuery ? (
              <TouchableOpacity onPress={() => handleSearch('')}>
                <Ionicons name="close-circle" size={20} color={colors.grey} />
              </TouchableOpacity>
            ) : null}
          </View>
        </View>

        <Text className="mb-2 text-base font-semibold" style={{ color: colors.grey }}>
          Friends
        </Text>

        {loading ? (
          <View className="flex-1 items-center justify-center py-10">
            <ActivityIndicator size="large" color={colors.primary} />
            <Text className="mt-4 text-center text-base" style={{ color: colors.grey }}>
              Loading friends...
            </Text>
          </View>
        ) : friends.length === 0 ? (
          <View className="flex-1 items-center justify-center py-10">
            <Ionicons name="people" size={48} color={colors.grey} />
            <Text
              className="mt-4 text-center font-medium text-base"
              style={{ color: colors.foreground }}>
              No Friends
            </Text>
            <Text className="mt-2 text-center text-sm" style={{ color: colors.grey }}>
              Add some friends to start chatting!
            </Text>
          </View>
        ) : filteredFriends.length > 0 ? (
          <FlatList
            data={filteredFriends}
            keyExtractor={(item) => item.id}
            renderItem={renderFriend}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 40 }}
          />
        ) : (
          <View className="flex-1 items-center justify-center py-10">
            <Ionicons name="search" size={48} color={colors.grey} />
            <Text className="mt-4 text-center text-base" style={{ color: colors.grey }}>
              No friends found with "{searchQuery}"
            </Text>
          </View>
        )}
      </View>
    </BottomSheetView>
  );
};

export default NewChatSheet;
